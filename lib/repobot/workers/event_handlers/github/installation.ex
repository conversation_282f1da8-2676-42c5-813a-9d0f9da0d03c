defmodule Repobot.Workers.EventHandlers.GitHub.Installation do
  @moduledoc """
  Oban worker for handling GitHub installation webhook events.

  This worker processes installation events from GitHub webhooks using the event-sourcing pattern,
  porting the logic from InstallationHandler directly into the worker for better consistency.
  """

  use Repobot.Workers.EventHandler

  alias <PERSON>obot.{Accounts, Repo}
  alias Repobot.Accounts.Organization

  @impl true
  def handle(%Events.Event{} = event) do
    action = get_in(event.payload, ["action"])
    account_login = get_in(event.payload, ["installation", "account", "login"])

    Logger.info("Processing installation event",
      event_id: event.id,
      organization_id: event.organization_id,
      action: action,
      account: account_login
    )

    case action do
      "created" ->
        handle_installation_created(event.payload["installation"])

      "deleted" ->
        handle_installation_deleted(event.payload["installation"])

      _ ->
        Logger.info("Unhandled installation action: #{action}",
          event_id: event.id,
          action: action,
          account: account_login
        )

        :ok
    end
    |> case do
      :ok ->
        Logger.info("Installation event processed successfully",
          event_id: event.id,
          organization_id: event.organization_id,
          action: action,
          account: account_login
        )

        :ok

      {:error, reason} ->
        Logger.error("Installation event processing failed",
          event_id: event.id,
          organization_id: event.organization_id,
          action: action,
          account: account_login,
          error: inspect(reason)
        )

        {:error, reason}
    end
  end

  defp handle_installation_created(%{"id" => installation_id, "account" => account}) do
    case find_organization(account) do
      nil ->
        Logger.info("No organization found for account: #{account["login"]}")
        :ok

      org ->
        Logger.info("Updating installation_id for organization: #{org.name}")

        case Repo.update(Organization.changeset(org, %{installation_id: installation_id})) do
          {:ok, _org} ->
            Logger.info("Successfully updated installation_id for organization: #{org.name}")
            :ok

          {:error, reason} ->
            Logger.error("Failed to update installation_id: #{inspect(reason)}")
            {:error, "Failed to update installation_id"}
        end
    end
  end

  defp handle_installation_deleted(%{"id" => _installation_id, "account" => account}) do
    case find_organization(account) do
      nil ->
        Logger.info("No organization found for account: #{account["login"]}")
        :ok

      org ->
        Logger.info("Removing installation_id for organization: #{org.name}")

        case Repo.update(Organization.changeset(org, %{installation_id: nil})) do
          {:ok, _org} ->
            Logger.info("Successfully removed installation_id for organization: #{org.name}")
            :ok

          {:error, reason} ->
            Logger.error("Failed to remove installation_id: #{inspect(reason)}")
            {:error, "Failed to remove installation_id"}
        end
    end
  end

  defp find_organization(%{"login" => login}) do
    case Accounts.get_organization_by_name(login) do
      nil ->
        Logger.info("Organization not found: #{login}")
        nil

      org ->
        org
    end
  end
end
