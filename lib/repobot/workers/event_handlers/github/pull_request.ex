defmodule Repobot.Workers.EventHandlers.GitHub.PullRequest do
  @moduledoc """
  Oban worker for handling GitHub pull request webhook events.

  This worker processes pull request events that have been stored
  in the events table and updates pull request status in the database.
  """

  use Repobot.Workers.EventHandler, max_attempts: 3

  alias Repobot.{Repositories, PullRequests}

  @impl true
  def handle(%Events.Event{} = event) do
    case process_pull_request_event(event) do
      :ok ->
        :ok

      {:error, reason} ->
        {:error, reason}
    end
  end

  # Process the pull request event
  defp process_pull_request_event(%Events.Event{} = event) do
    payload = event.payload
    pr_data = payload["pull_request"]
    repository_data = payload["repository"]

    pr_number = pr_data["number"]
    repository_full_name = repository_data["full_name"]
    action = payload["action"]
    merged = pr_data["merged"]

    Logger.info("Processing pull request event",
      event_id: event.id,
      repository: repository_full_name,
      pr_number: pr_number,
      action: action,
      merged: merged
    )

    # Find the repository
    case Repositories.get_repository_by(full_name: repository_full_name) do
      nil ->
        Logger.info("Repository not found for pull request event",
          event_id: event.id,
          repository: repository_full_name,
          pr_number: pr_number
        )

        :ok

      repository ->
        # Process the pull request status update
        process_pull_request_status_update(
          event,
          repository,
          pr_number,
          action,
          merged
        )
    end
  end

  # Process pull request status update
  defp process_pull_request_status_update(event, repository, pr_number, action, merged) do
    # Find the pull request in our database
    case PullRequests.get_pull_request_by(
           repository: repository.full_name,
           pull_request_number: pr_number
         ) do
      nil ->
        Logger.info("Pull request not found in database",
          event_id: event.id,
          repository: repository.full_name,
          pr_number: pr_number,
          action: action
        )

        :ok

      pull_request ->
        # Determine the new status based on action and merged state
        case determine_new_status(action, merged, pull_request.status) do
          nil ->
            Logger.info("No status change needed for pull request",
              event_id: event.id,
              repository: repository.full_name,
              pr_number: pr_number,
              action: action,
              current_status: pull_request.status
            )

            :ok

          new_status ->
            update_pull_request_status(event, pull_request, new_status)
        end
    end
  end

  # Update pull request status in the database
  defp update_pull_request_status(event, pull_request, new_status) do
    case PullRequests.update_pull_request(pull_request, %{status: new_status}) do
      {:ok, updated_pr} ->
        Logger.info("Pull request status updated successfully",
          event_id: event.id,
          pull_request_id: updated_pr.id,
          repository: updated_pr.repository,
          pr_number: updated_pr.pull_request_number,
          old_status: pull_request.status,
          new_status: new_status
        )

        :ok

      {:error, changeset} ->
        Logger.error("Failed to update pull request status",
          event_id: event.id,
          pull_request_id: pull_request.id,
          repository: pull_request.repository,
          pr_number: pull_request.pull_request_number,
          new_status: new_status,
          errors: inspect(changeset.errors)
        )

        {:error, "Failed to update pull request status"}
    end
  end

  # Determine the new status based on GitHub action and merged state
  defp determine_new_status(action, merged, current_status) do
    case {action, merged, current_status} do
      # Pull request was closed and merged
      {"closed", true, status} when status != "merged" ->
        "merged"

      # Pull request was closed but not merged
      {"closed", false, status} when status != "closed" ->
        "closed"

      # Pull request was reopened
      {"reopened", _, status} when status != "open" ->
        "open"

      # No status change needed for other actions or if already in correct status
      _ ->
        nil
    end
  end
end
