defmodule RepobotWeb.WebhookController.PullRequestTest do
  use RepobotWeb.ConnCase, async: false
  use Oban.Testing, repo: Repobot.Repo
  use Repobot.Test.Fixtures

  import Mox

  alias <PERSON>obot.{Events, Repo}
  alias Repobot.Events.Event
  alias Repobot.PullRequest
  alias Repobot.Workers.EventHandlers.GitHub.PullRequest, as: PullRequestWorker

  setup :verify_on_exit!

  setup do
    # Set up Oban test mode
    :ok = Oban.Testing.with_testing_mode(:inline, fn -> :ok end)

    # Setup signature verifier mock
    Repobot.Test.SignatureVerifierMock
    |> stub(:verify_signature, fn _conn -> :ok end)

    user = create_user()
    organization = user.default_organization

    repository =
      create_repository(%{
        name: "test-repo",
        user_id: user.id,
        organization_id: organization.id
      })

    source_file =
      create_source_file(%{
        name: "test.ex",
        target_path: "test.ex",
        content: "test content",
        source_repository_id: repository.id,
        user_id: user.id,
        organization_id: organization.id
      })

    %{
      user: user,
      organization: organization,
      repository: repository,
      source_file: source_file
    }
  end

  describe "webhook controller pull request event handling" do
    test "creates event and schedules worker for pull request opened", %{
      conn: conn,
      repository: repository
    } do
      payload = %{
        "action" => "opened",
        "pull_request" => %{
          "number" => 123,
          "title" => "Test PR",
          "html_url" => "https://github.com/#{repository.full_name}/pull/123",
          "merged" => false
        },
        "repository" => %{
          "full_name" => repository.full_name
        }
      }

      # Count events before webhook
      event_count_before = length(Repo.all(Event))

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "pull_request")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify that an event was created
      events_after = Repo.all(Event)
      assert length(events_after) == event_count_before + 1

      # Get the latest event
      latest_event = Enum.max_by(events_after, & &1.inserted_at)
      assert latest_event.type == "github.pull_request.opened"
      assert latest_event.organization_id == repository.organization_id
      assert latest_event.repository_id == repository.id
      assert latest_event.payload["action"] == "opened"
      assert latest_event.payload["pull_request"]["number"] == 123
    end

    test "handles pull request event for non-existent repository", %{conn: conn} do
      payload = %{
        "action" => "closed",
        "pull_request" => %{
          "number" => 123,
          "title" => "Test PR",
          "html_url" => "https://github.com/non/existent/pull/123",
          "merged" => true
        },
        "repository" => %{
          "full_name" => "non/existent"
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "pull_request")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end
  end

  describe "PullRequestWorker" do
    test "processes pull request closed event and updates status to merged", %{
      repository: repository,
      source_file: source_file
    } do
      # Create a pull request in open status
      pull_request =
        create_pull_request(%{
          repository: repository.full_name,
          pull_request_number: 123,
          status: "open",
          source_file_id: source_file.id
        })

      # Create event for pull request closed (merged)
      payload = %{
        "action" => "closed",
        "pull_request" => %{
          "number" => 123,
          "title" => "Test PR",
          "html_url" => "https://github.com/#{repository.full_name}/pull/123",
          "merged" => true
        },
        "repository" => %{
          "full_name" => repository.full_name
        }
      }

      {:ok, event} =
        Events.log_event(
          "github.pull_request.closed",
          payload,
          repository.organization_id,
          nil,
          repository.id,
          "pending"
        )

      # Create and perform the job
      job = %Oban.Job{
        id: 1,
        worker: "Repobot.Workers.EventHandlers.GitHub.PullRequest",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => event.id}
      }

      # Perform the job
      result = PullRequestWorker.perform(job)

      # Assert the job succeeded
      assert result == :ok

      # Assert the pull request status was updated
      updated_pr = Repo.get!(PullRequest, pull_request.id)
      assert updated_pr.status == "merged"

      # Assert the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"
    end

    test "processes pull request closed event and updates status to closed", %{
      repository: repository,
      source_file: source_file
    } do
      # Create a pull request in open status
      pull_request =
        create_pull_request(%{
          repository: repository.full_name,
          pull_request_number: 124,
          status: "open",
          source_file_id: source_file.id
        })

      # Create event for pull request closed (not merged)
      payload = %{
        "action" => "closed",
        "pull_request" => %{
          "number" => 124,
          "title" => "Test PR",
          "html_url" => "https://github.com/#{repository.full_name}/pull/124",
          "merged" => false
        },
        "repository" => %{
          "full_name" => repository.full_name
        }
      }

      {:ok, event} =
        Events.log_event(
          "github.pull_request.closed",
          payload,
          repository.organization_id,
          nil,
          repository.id,
          "pending"
        )

      # Create and perform the job
      job = %Oban.Job{
        id: 2,
        worker: "Repobot.Workers.EventHandlers.GitHub.PullRequest",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => event.id}
      }

      # Perform the job
      result = PullRequestWorker.perform(job)

      # Assert the job succeeded
      assert result == :ok

      # Assert the pull request status was updated
      updated_pr = Repo.get!(PullRequest, pull_request.id)
      assert updated_pr.status == "closed"
    end

    test "processes pull request reopened event", %{
      repository: repository,
      source_file: source_file
    } do
      # Create a pull request in closed status
      pull_request =
        create_pull_request(%{
          repository: repository.full_name,
          pull_request_number: 125,
          status: "closed",
          source_file_id: source_file.id
        })

      # Create event for pull request reopened
      payload = %{
        "action" => "reopened",
        "pull_request" => %{
          "number" => 125,
          "title" => "Test PR",
          "html_url" => "https://github.com/#{repository.full_name}/pull/125",
          "merged" => false
        },
        "repository" => %{
          "full_name" => repository.full_name
        }
      }

      {:ok, event} =
        Events.log_event(
          "github.pull_request.reopened",
          payload,
          repository.organization_id,
          nil,
          repository.id,
          "pending"
        )

      # Create and perform the job
      job = %Oban.Job{
        id: 3,
        worker: "Repobot.Workers.EventHandlers.GitHub.PullRequest",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => event.id}
      }

      # Perform the job
      result = PullRequestWorker.perform(job)

      # Assert the job succeeded
      assert result == :ok

      # Assert the pull request status was updated
      updated_pr = Repo.get!(PullRequest, pull_request.id)
      assert updated_pr.status == "open"
    end

    test "handles pull request event for non-existent repository", %{
      organization: organization
    } do
      # Create event for pull request on non-existent repository
      payload = %{
        "action" => "closed",
        "pull_request" => %{
          "number" => 126,
          "title" => "Test PR",
          "html_url" => "https://github.com/non/existent/pull/126",
          "merged" => true
        },
        "repository" => %{
          "full_name" => "non/existent"
        }
      }

      {:ok, event} =
        Events.log_event(
          "github.pull_request.closed",
          payload,
          organization.id,
          nil,
          nil,
          "pending"
        )

      # Create and perform the job
      job = %Oban.Job{
        id: 4,
        worker: "Repobot.Workers.EventHandlers.GitHub.PullRequest",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => event.id}
      }

      # Perform the job
      result = PullRequestWorker.perform(job)

      # Assert the job succeeded (no error for missing repository)
      assert result == :ok
    end

    test "handles pull request event for non-existent pull request", %{
      repository: repository
    } do
      # Create event for pull request that doesn't exist in our database
      payload = %{
        "action" => "closed",
        "pull_request" => %{
          "number" => 999,
          "title" => "Non-existent PR",
          "html_url" => "https://github.com/#{repository.full_name}/pull/999",
          "merged" => true
        },
        "repository" => %{
          "full_name" => repository.full_name
        }
      }

      {:ok, event} =
        Events.log_event(
          "github.pull_request.closed",
          payload,
          repository.organization_id,
          nil,
          repository.id,
          "pending"
        )

      # Create and perform the job
      job = %Oban.Job{
        id: 5,
        worker: "Repobot.Workers.EventHandlers.GitHub.PullRequest",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => event.id}
      }

      # Perform the job
      result = PullRequestWorker.perform(job)

      # Assert the job succeeded (no error for missing pull request)
      assert result == :ok
    end

    test "handles pull request event with no status change needed", %{
      repository: repository,
      source_file: source_file
    } do
      # Create a pull request already in merged status
      pull_request =
        create_pull_request(%{
          repository: repository.full_name,
          pull_request_number: 127,
          status: "merged",
          source_file_id: source_file.id
        })

      # Create event for pull request closed (merged) - should not change status
      payload = %{
        "action" => "closed",
        "pull_request" => %{
          "number" => 127,
          "title" => "Test PR",
          "html_url" => "https://github.com/#{repository.full_name}/pull/127",
          "merged" => true
        },
        "repository" => %{
          "full_name" => repository.full_name
        }
      }

      {:ok, event} =
        Events.log_event(
          "github.pull_request.closed",
          payload,
          repository.organization_id,
          nil,
          repository.id,
          "pending"
        )

      # Create and perform the job
      job = %Oban.Job{
        id: 6,
        worker: "Repobot.Workers.EventHandlers.GitHub.PullRequest",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => event.id}
      }

      # Perform the job
      result = PullRequestWorker.perform(job)

      # Assert the job succeeded
      assert result == :ok

      # Assert the pull request status remained the same
      updated_pr = Repo.get!(PullRequest, pull_request.id)
      assert updated_pr.status == "merged"
    end
  end
end
